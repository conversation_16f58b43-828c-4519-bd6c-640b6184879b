{"configurations": [{"name": "Python: Xiaozhi Server", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/main/xiaozhi-server/server.py", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}/main/xiaozhi-server"}}, {"name": "Python: Manager API", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/main/manager-api/app.py", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}/main/manager-api"}}]}