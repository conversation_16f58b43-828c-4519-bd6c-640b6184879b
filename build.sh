##!/bin/bash
#source /tmp/evn
#sed -i "s@ENV@${ENV}@" Makefile
#echo 'monitorPort=9902' >>/tmp/evn
#
#if [[ "${ENV}" == "prod" ]]; then
#  sed -i "/CMD.*/d" Dockerfile
#  echo 'CMD ["java","-Xms1024m","-Xmx3584m","-Dspring.profiles.include=k8s","-XX:ErrorFile=/app/data/sensors/ms-tech-admin-web-hs_err.log", "-XX:+HeapDumpOnOutOfMemoryError","-XX:HeapDumpPath=/app/data/sensors/ms-tech-admin-web-tech_heapdump.hprof", "-jar","server.jar"]' >>Dockerfile
#fi
