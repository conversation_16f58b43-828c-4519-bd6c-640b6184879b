# 如果你只想轻量化安装xiaozhi-server，只使用本地的配置文件，不需要理会这个文件，不需要改动本文件任何东西
# 如果你想从manager-api获取配置，请往下看：
# 请将本文件复制到xiaozhi-server/data目录下，没有data目录，请创建一个，并将复制过去的文件命名为.config.yaml
# 注意如果data目录有.config.yaml文件，请先删除它
# 先启动manager-api和manager-web，注册一个账号，第一个注册的账号为管理员
# 使用管理员，进入【参数管理】页面，找到【server.secret】，复制它到参数值，注意每次从零部署，server.secret都会变化
# 打开本data目录下的.config.yaml文件，修改manager-api.secret为刚才复制出来的server.secret
server:
  ip: 0.0.0.0
  port: 8000
  # http服务的端口，用于视觉分析接口
  http_port: 8003
  # 视觉分析接口地址
  # 向设备发送的视觉分析的接口地址
  # 如果按下面默认的写法，系统会自动生成视觉识别地址，并输出在启动日志里，这个地址你可以直接用浏览器访问确认一下
  # 当你使用docker部署或使用公网部署(使用ssl、域名)时，不一定准确
  # 所以如果你使用docker部署时，将vision_explain设置成局域网地址
  # 如果你使用公网部署时，将vision_explain设置成公网地址
  vision_explain: http://你的ip或者域名:端口号/mcp/vision/explain
manager-api:
  # 你的manager-api的地址，最好使用局域网ip
  # 如果使用docker部署，请使用填写成 http://xiaozhi-esp32-server-web:8002/xiaozhi
  url: http://127.0.0.1:8002/xiaozhi
  # 你的manager-api的token，就是刚才复制出来的server.secret
  secret: 你的server.secret值