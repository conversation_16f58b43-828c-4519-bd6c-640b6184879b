#!/bin/bash

# MIMO Server 安装脚本
# 用于下载必要的模型文件

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 创建目录
create_directory() {
    local dir=$1
    if [ ! -d "$dir" ]; then
        log_info "创建目录: $dir"
        mkdir -p "$dir"
    else
        log_info "目录已存在: $dir"
    fi
}

# 下载文件函数
download_file() {
    local url=$1
    local output_path=$2
    local filename=$(basename "$output_path")

    log_info "开始下载: $filename"
    log_info "URL: $url"
    log_info "保存到: $output_path"

    # 检查文件是否已存在
    if [ -f "$output_path" ]; then
        log_warning "文件已存在: $output_path"
        read -p "是否重新下载? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过下载: $filename"
            return 0
        fi
        rm -f "$output_path"
    fi

    # 使用 wget 或 curl 下载
    if command -v wget &> /dev/null; then
        if wget --progress=bar:force:noscroll -O "$output_path" "$url"; then
            log_success "下载完成: $filename"
        else
            log_error "下载失败: $filename"
            return 1
        fi
    elif command -v curl &> /dev/null; then
        if curl -L --progress-bar -o "$output_path" "$url"; then
            log_success "下载完成: $filename"
        else
            log_error "下载失败: $filename"
            return 1
        fi
    else
        log_error "未找到 wget 或 curl 命令，无法下载文件"
        return 1
    fi
}

# 解压文件函数
extract_file() {
    local file_path=$1
    local extract_to=$2
    local filename=$(basename "$file_path")

    log_info "解压文件: $filename 到 $extract_to"

    case "$file_path" in
        *.tar.gz)
            if tar -xzf "$file_path" -C "$extract_to"; then
                log_success "解压完成: $filename"
            else
                log_error "解压失败: $filename"
                return 1
            fi
            ;;
        *.zip)
            if unzip -q "$file_path" -d "$extract_to"; then
                log_success "解压完成: $filename"
            else
                log_error "解压失败: $filename"
                return 1
            fi
            ;;
        *)
            log_error "不支持的文件格式: $filename"
            return 1
            ;;
    esac
}

# 主安装函数
main() {
    log_info "开始 MIMO Server 模型文件安装..."

    # 检查必要的命令
    log_info "检查系统依赖..."
    if command -v wget &> /dev/null; then
        log_success "找到 wget 命令"
    elif command -v curl &> /dev/null; then
        log_success "找到 curl 命令"
    else
        log_error "未找到 wget 或 curl 命令，请先安装其中一个"
        exit 1
    fi

    # 创建模型目录
    log_info "创建模型目录..."
    create_directory "models"
    create_directory "models/SenseVoiceSmall"
    create_directory "models/snakers4_silero-vad"

    # 下载 SenseVoiceSmall 模型
    log_info "========================================="
    log_info "下载 SenseVoiceSmall 模型文件..."
    log_info "========================================="

    SENSEVOICE_URL="https://modelscope.cn/models/iic/SenseVoiceSmall/resolve/master/model.pt"
    SENSEVOICE_PATH="models/SenseVoiceSmall/model.pt"

    if ! download_file "$SENSEVOICE_URL" "$SENSEVOICE_PATH"; then
        log_error "SenseVoiceSmall 模型下载失败"
        exit 1
    fi

    # 下载 Silero VAD 模型
    log_info "========================================="
    log_info "下载 Silero VAD 模型文件..."
    log_info "========================================="

    SILERO_URL="https://github.com/snakers4/silero-vad/archive/refs/tags/v5.1.2.tar.gz"
    SILERO_TEMP_PATH="models/silero-vad-5.1.2.tar.gz"
    SILERO_EXTRACT_PATH="models"

    if ! download_file "$SILERO_URL" "$SILERO_TEMP_PATH"; then
        log_error "Silero VAD 模型下载失败"
        exit 1
    fi

    # 解压 Silero VAD
    log_info "解压 Silero VAD 文件..."
    if ! extract_file "$SILERO_TEMP_PATH" "$SILERO_EXTRACT_PATH"; then
        log_error "Silero VAD 解压失败"
        exit 1
    fi

    # 重命名解压后的目录
    if [ -d "models/silero-vad-5.1.2" ]; then
        log_info "重命名目录: silero-vad-5.1.2 -> snakers4_silero-vad"
        mv "models/silero-vad-5.1.2" "models/snakers4_silero-vad"
    fi

    # 清理临时文件
    log_info "清理临时文件..."
    rm -f "$SILERO_TEMP_PATH"

    # 验证下载结果
    log_info "========================================="
    log_info "验证下载结果..."
    log_info "========================================="

    if [ -f "$SENSEVOICE_PATH" ]; then
        file_size=$(du -h "$SENSEVOICE_PATH" | cut -f1)
        log_success "SenseVoiceSmall 模型文件存在 (大小: $file_size)"
    else
        log_error "SenseVoiceSmall 模型文件不存在"
        exit 1
    fi

    if [ -d "models/snakers4_silero-vad" ]; then
        log_success "Silero VAD 模型目录存在"
        file_count=$(find "models/snakers4_silero-vad" -type f | wc -l)
        log_info "包含 $file_count 个文件"
    else
        log_error "Silero VAD 模型目录不存在"
        exit 1
    fi

    log_success "========================================="
    log_success "所有模型文件安装完成！"
    log_success "========================================="

    log_info "模型文件位置:"
    log_info "  - SenseVoiceSmall: $SENSEVOICE_PATH"
    log_info "  - Silero VAD: models/snakers4_silero-vad/"

    log_info "现在可以运行 MIMO Server 了！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi